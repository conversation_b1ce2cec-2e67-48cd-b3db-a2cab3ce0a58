
#ifndef __KEY_H__
#define __KEY_H__


#include "main.h"
#include <stdint.h>
#include <stdbool.h>
#include "app_timer.h"
#include "nrf_gpio.h"


#define KEY_ANTI_SHAKE_MS           (10)
#define KEY_LONG_PRESS_START_MS     (1000)
#define KEY_LONG_PRESS_INTERVAL_MS  (300)
#define KEY_DOUBLE_CLICK_WINDOW_MS  (500)


#define KEY_USER_PIN	GPIO_KEY_USER
#define KEY_PRESS_PIN	GPIO_KEY_PRESS
#define KEY_UP_PIN		GPIO_KEY_UP
#define KEY_DOWN_PIN	GPIO_KEY_DOWN

void key_init(void);

void key_deinit(void);

void key_system_recovery(void);
void key_system_reset_recovery(void);
void key_disable_all(void);
void key_enable_all(void);
bool key_is_enabled(void);
void wait_user_key_long_press(void);
#endif
