#include "pwm.h"
#include "nrf_drv_pwm.h"
#include "app_pwm.h"

APP_PWM_INSTANCE(PWM2, 2);
static nrf_drv_pwm_t m_pwm_led = NRF_DRV_PWM_INSTANCE(0);//gb
static nrf_pwm_values_individual_t m_pwm_b_g_seq_values;
static nrf_pwm_sequence_t const    m_pwm_b_g_seq = {
    .values.p_individual = &m_pwm_b_g_seq_values,
    .length              = NRF_PWM_VALUES_LENGTH(m_pwm_b_g_seq_values),
    .repeats             = 0,
    .end_delay           = 0
};

//状态指示灯
static nrf_drv_pwm_t m_pwm_state = NRF_DRV_PWM_INSTANCE(1); //state les
static nrf_pwm_values_individual_t pwm_seq_buffer[PWM_DATA_LEN];
static uint8_t led_data[TX1812MXA5_LED_NUM][3]; // GRB顺序存储
static bool is_initialized = false;
static nrf_pwm_sequence_t const    m_pwm_state_seq = {
    .values.p_individual = pwm_seq_buffer,
    .length              = PWM_DATA_LEN,
    .repeats             = 0,
    .end_delay           = 0
};

// 预定义颜色表 (RGB格式)
static const uint8_t predefined_colors[][3] = {
    {0,   0,   0},   // OFF
    {255, 0,   0},   // RED
    {0,   255, 0},   // GREEN
    {0,   0,   255}, // BLUE
    {255, 255, 0},   // YELLOW
    {255, 0,   255}, // PURPLE
    {0,   255, 255}, // CYAN
    {255, 255, 255}  // WHITE
};


//PWM
//PWM频率80kHz
void pwm_led_init(void)
{
	  nrf_gpio_cfg_output(LED_G_CUT_PIN);
    nrf_gpio_pin_clear(LED_G_CUT_PIN);

    nrf_gpio_cfg_output(LED_B_CUT_PIN);
    nrf_gpio_pin_clear(LED_B_CUT_PIN);
    nrf_drv_pwm_config_t const config = {
        .output_pins =
        {
            PWM_LED_PIN,             // channel 0
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 1
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 2
            NRF_DRV_PWM_PIN_NOT_USED    // channel 3
        },
        .irq_priority = APP_IRQ_PRIORITY_LOWEST,
        .base_clock   = NRF_PWM_CLK_16MHz,      //时钟
        .count_mode   = NRF_PWM_MODE_UP,
        .top_value    = 100,                   //周期，最大值是0x7FFF
        .load_mode    = NRF_PWM_LOAD_INDIVIDUAL,
        .step_mode    = NRF_PWM_STEP_AUTO
    };
    APP_ERROR_CHECK(nrf_drv_pwm_init(&m_pwm_led, &config, NULL));

    //比较值
    m_pwm_b_g_seq_values.channel_0 = 100;
    m_pwm_b_g_seq_values.channel_1 = 0;
    m_pwm_b_g_seq_values.channel_2 = 0;
    m_pwm_b_g_seq_values.channel_3 = 0;

    (void)nrf_drv_pwm_simple_playback(&m_pwm_led, &m_pwm_b_g_seq, 1, NRF_DRV_PWM_FLAG_LOOP);
}


//PWM_YELLOE和PWM_BLUE反初始化
void pwm_led_deinit(void)
{
    nrf_drv_pwm_uninit(&m_pwm_led);
//    nrf_gpio_cfg_input(PWM_BLUE_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(PWM_LED_PIN, NRF_GPIO_PIN_NOPULL);
}

//设置PWM_GREEN占空比
//duty: 0~100
void pwm_led_set_duty(uint16_t duty)
{
    APP_ERROR_CHECK_BOOL(duty <= 100);
    m_pwm_b_g_seq_values.channel_0 = 100 - duty;
}

void pwm_state_init(void)
{
    nrf_drv_pwm_config_t const config = {
        .output_pins =
        {
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 0
            TX1812MXA5_PWM_GPIO,   			// channel 1
            NRF_DRV_PWM_PIN_NOT_USED,   // channel 2
            NRF_DRV_PWM_PIN_NOT_USED    // channel 3
        },
        .irq_priority = APP_IRQ_PRIORITY_LOWEST,
        .base_clock   = NRF_PWM_CLK_16MHz,      //时钟
        .count_mode   = NRF_PWM_MODE_UP,
        .top_value    = PWM_PERIOD_TICKS,
        .load_mode    = NRF_PWM_LOAD_INDIVIDUAL,
        .step_mode    = NRF_PWM_STEP_AUTO
    };
    APP_ERROR_CHECK(nrf_drv_pwm_init(&m_pwm_state, &config, NULL));

}

/**
* @brief 编码PWM数据
*
* 将RGB数据编码为PWM时序数据
*/
static void encode_pwm_data(void)
{
    uint16_t bit_index = 0;

    for (uint8_t i = 0; i < TX1812MXA5_LED_NUM; i++) {
        // TX1812MXA5使用GRB顺序
        for (uint8_t c = 0; c < 3; c++) {
            uint8_t val = led_data[i][c];
            for (int8_t b = 7; b >= 0; b--) {
                uint8_t bit = (val >> b) & 0x01;
                pwm_seq_buffer[bit_index++].channel_1 = (bit ? DUTY_0 : DUTY_1);
            }
        }
    }

    // 添加结束位 (占空比为0)
    pwm_seq_buffer[bit_index].channel_1 = 14;
}

/**
* @brief 应用亮度到RGB值
*
* @param r 红色分量指针
* @param g 绿色分量指针
* @param b 蓝色分量指针
* @param brightness 亮度 (0-100)
*/
static void apply_brightness(uint8_t *r, uint8_t *g, uint8_t *b, uint8_t brightness)
{
    if (brightness > 100) brightness = 100;

    *r = (*r * brightness) / 100;
    *g = (*g * brightness) / 100;
    *b = (*b * brightness) / 100;
}



uint32_t tx1812mxa5_set_color(uint8_t index, uint8_t r, uint8_t g, uint8_t b)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    if (index >= TX1812MXA5_LED_NUM) {
        return NRF_ERROR_INVALID_PARAM;
    }

    // TX1812MXA5使用GRB顺序
    led_data[index][0] = g;  // Green
    led_data[index][1] = r;  // Red
    led_data[index][2] = b;  // Blue

    return NRF_SUCCESS;
}

uint32_t tx1812mxa5_set_color_brightness(uint8_t index, uint8_t r, uint8_t g, uint8_t b, uint8_t brightness)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    // 应用亮度
    apply_brightness(&r, &g, &b, brightness);

    return tx1812mxa5_set_color(index, r, g, b);
}

uint32_t tx1812mxa5_set_predefined_color(uint8_t index, tx1812mxa5_color_t color, uint8_t brightness)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    if (color >= sizeof(predefined_colors) / sizeof(predefined_colors[0])) {
        return NRF_ERROR_INVALID_PARAM;
    }

    uint8_t r = predefined_colors[color][0];
    uint8_t g = predefined_colors[color][1];
    uint8_t b = predefined_colors[color][2];

    return tx1812mxa5_set_color_brightness(index, r, g, b, brightness);
}

uint32_t tx1812mxa5_set_all_leds(uint8_t r, uint8_t g, uint8_t b, uint8_t brightness)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    for (uint8_t i = 0; i < TX1812MXA5_LED_NUM; i++) {
        uint32_t err_code = tx1812mxa5_set_color_brightness(i, r, g, b, brightness);
        if (err_code != NRF_SUCCESS) {
            return err_code;
        }
    }

    return NRF_SUCCESS;
}

uint32_t tx1812mxa5_update(void)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    // 编码PWM数据
    encode_pwm_data();

    // 发送PWM序列
    ret_code_t err_code = nrf_drv_pwm_simple_playback(&m_pwm_state, &m_pwm_state_seq, 1, NRF_DRV_PWM_FLAG_LOOP);
    if (err_code != NRF_SUCCESS) {
        NRF_LOG_ERROR("TX1812MXA5 PWM playback failed: 0x%08X", err_code);
        return err_code;
    }


    return NRF_SUCCESS;
}

uint32_t tx1812mxa5_clear(void)
{
    if (!is_initialized) {
        return NRF_ERROR_INVALID_STATE;
    }

    // 清除所有LED数据
    memset(led_data, 0, sizeof(led_data));

    // 更新显示
    return tx1812mxa5_update();
}

uint32_t tx1812mxa5_deinit(void)
{
    if (!is_initialized) {
        return NRF_SUCCESS;
    }

    // 清除LED
    tx1812mxa5_clear();

    // 反初始化PWM
    nrf_drv_pwm_uninit(&m_pwm_state);

    is_initialized = false;

    NRF_LOG_INFO("TX1812MXA5 driver deinitialized");
    return NRF_SUCCESS;
}

void pwm_state_deinit(void)
{
    if (!is_initialized) {
        return;
    }

    // 清除LED
    tx1812mxa5_clear();

    // 反初始化PWM
    nrf_drv_pwm_uninit(&m_pwm_state);

    is_initialized = false;

    NRF_LOG_INFO("PWM state deinitialized");
}

void test(void)
{
    int i;
    for(i = 0; i < 8; i++) {
        pwm_seq_buffer[i].channel_1 = 10;
    }
    for(i = 8; i < 16; i++) {
        pwm_seq_buffer[i].channel_1 = 10;
    }
    for(i = 16; i < 24; i++) {
        pwm_seq_buffer[i].channel_1 = 4;
    }
    pwm_seq_buffer[24].channel_1 = 14;
    (void)nrf_drv_pwm_simple_playback(&m_pwm_state, &m_pwm_state_seq, 1, NRF_DRV_PWM_FLAG_LOOP);
}