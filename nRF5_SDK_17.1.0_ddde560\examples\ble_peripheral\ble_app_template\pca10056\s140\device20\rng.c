#include "rng.h"





//获取一个字节的随机数
uint8_t rng_get_byte_with_wait(void)
{
    while (1) {
        uint8_t rng_byte;
        ret_code_t ret_code = sd_rand_application_vector_get(&rng_byte, 1);
        if (ret_code == NRF_ERROR_SOC_RAND_NOT_ENOUGH_VALUES) {
            continue;
        } else {
            APP_ERROR_CHECK(ret_code);
            return rng_byte;
        }
    }
}

//获取一个字的随机数
uint32_t rng_get_word_with_wait(void)
{
    uint8_t b1 = rng_get_byte_with_wait();
    uint8_t b2 = rng_get_byte_with_wait();
    uint8_t b3 = rng_get_byte_with_wait();
    uint8_t b4 = rng_get_byte_with_wait();

    uint32_t rng_word = (b1 << 24) | (b2 << 16) | (b3 << 8) | (b4 << 0);

    return rng_word;
}
