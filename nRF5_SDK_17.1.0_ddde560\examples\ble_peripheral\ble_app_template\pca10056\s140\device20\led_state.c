#include "led_state.h"
#include "pwm.h"


static bool m_is_led_state_on = false;
static uint32_t m_led_state_bright = 60;


//LED_STATE指示灯初始化
void led_state_init(void)
{
    led_state_off();
}


//打开指示灯
void led_state_on(void)
{
    m_is_led_state_on = true;
    pwm_led_state_set_duty_percent(m_led_state_bright);
}


//关闭指示灯
void led_state_off(void)
{
    m_is_led_state_on = false;
    pwm_led_state_set_duty_percent(0);
}


//设置指示灯亮度
void led_state_set_bright(uint32_t bright)
{
    APP_ERROR_CHECK_BOOL(bright <= 100);

    m_led_state_bright = bright;
    if (m_is_led_state_on) {
        pwm_led_state_set_duty_percent(bright);
    }
}

