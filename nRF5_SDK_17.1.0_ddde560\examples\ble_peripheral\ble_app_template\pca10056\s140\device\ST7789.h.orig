#ifndef __ST7789_H__
#define __ST7789_H__


#include "main.h"
#include <stdint.h>
#include <stdbool.h>
#include "nrf.h"
#include "sdk_config.h"

#include "nordic_common.h"
#include "nrf.h"
#include "nrf_assert.h"

#include "nrf_gpio.h"
#include "nrf_delay.h"
#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

//LCD??????
#define LCD_DIRECTION					(3)
#if (LCD_DIRECTION == 0 || LCD_DIRECTION == 1)//??
#define LCD_HOR_RES 				(172)
#define LCD_VER_RES 				(320)
#elif (LCD_DIRECTION == 2 || LCD_DIRECTION == 3)//??
#define LCD_HOR_RES 				(320)
#define LCD_VER_RES 				(172)
#endif


void st7789_init(void);
void st7789_wr_aera(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t* color_p);
void st7789_fill_full(uint16_t color);
void st7789_wr_point(int16_t x, int16_t y, uint16_t color);

void close_LCD(void);

#endif
