#include "power.h"
#include "nrf_gpio.h"
#include "nrf_drv_gpiote.h"
#include "nrf_drv_power.h"
#include "app_timer.h"
#include "gui.h"
#include "sm.h"

#define STATE_STABLE_THRESHOLD  2  // 连续3次一样才切换状态
/**LGS4056H充电管理IC**/

APP_TIMER_DEF(m_lgs4056_timer);

static charge_state_t current_charge_state = CHARGE_STATE_IDLE;
static charge_state_t last_sampled_state = CHARGE_STATE_IDLE;
static uint8_t stable_count = 0;
static void lgs4056_timer_handler(void *p_context);
static void lgs4056_update_state(charge_state_t new_state);

void lgs4056_gpio_init(void)
{
    ret_code_t err_code;

    if (!nrf_drv_gpiote_is_init()) {
        err_code = nrf_drv_gpiote_init();
        APP_ERROR_CHECK(err_code);
    }

    // 配置引脚为上拉输入
    nrf_gpio_cfg_input(LGS4056_CHRG_PIN, NRF_GPIO_PIN_PULLUP);
    nrf_gpio_cfg_input(LGS4056_DONE_PIN, NRF_GPIO_PIN_PULLUP);

    // 创建并启动定时器
    err_code = app_timer_create(&m_lgs4056_timer, APP_TIMER_MODE_REPEATED, lgs4056_timer_handler);
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_start(m_lgs4056_timer, APP_TIMER_TICKS(300), NULL);  // 300ms 定时器轮询
    APP_ERROR_CHECK(err_code);
}

static void lgs4056_timer_handler(void *p_context)
{
    bool chrg = nrf_gpio_pin_read(LGS4056_CHRG_PIN);   // 0 = 正在充电
    bool done = nrf_gpio_pin_read(LGS4056_DONE_PIN);   // 0 = 充满

    charge_state_t sampled;

    if (chrg == 0) {
        sampled = CHARGE_STATE_CHARGING;
    } else if (done == 0) {
        sampled = CHARGE_STATE_CHARGED;
    } else {
        sampled = CHARGE_STATE_IDLE;
    }

    // 状态一致性判断
    if (sampled == last_sampled_state) {
        stable_count++;
        if (stable_count >= STATE_STABLE_THRESHOLD && sampled != current_charge_state) {
            lgs4056_update_state(sampled);
        }
    } else {
        stable_count = 0;
        last_sampled_state = sampled;
    }
}

static void lgs4056_update_state(charge_state_t new_state)
{
    current_charge_state = new_state;

    switch (new_state) {
    case CHARGE_STATE_CHARGING:
//        if (get_current_gui() != UI_SCREEN_CHARGED) {
//            set_current_gui(UI_SCREEN_CHARGED);
//            switch_to_next_screen(UI_SCREEN_CHARGED);
//        }
				// 更新充电界面的电量显示
        update_charged_screen_battery_level();
        break;

    case CHARGE_STATE_IDLE:
//        if (get_current_gui() != UI_SCREEN_RESULT1) {
//            set_current_gui(UI_SCREEN_RESULT1);
//            switch_to_next_screen(UI_SCREEN_RESULT1);
//        }
        break;

    case CHARGE_STATE_CHARGED:
        // 充电完成时也更新电量显示
//        if (get_current_gui() == UI_SCREEN_CHARGED) {
//            update_charged_screen_battery_level();
//        }
        break;
    }
}

//电量显示
void format_percentage(int value, char *output_buffer, size_t buffer_size)
{
    // 限制范围在 0~100
    if (value < 0) value = 0;
    if (value > 100) value = 100;

    snprintf(output_buffer, buffer_size, "%d%%", value);
}

//更新充电界面的电量显示
void update_charged_screen_battery_level(void) {
    lv_ui ui = get_ui();
    extern uint8_t get_SOC(void);  // 从CW2015获取电量

    // 获取当前电量百分比
    uint8_t battery_percent = get_SOC();

    // 格式化电量文字
    char battery_text[16];
    format_percentage(battery_percent, battery_text, sizeof(battery_text));

    // 更新充电界面的标签文字
    if (ui.charged_label_1 != NULL) {
        lv_label_set_text(ui.charged_label_1, battery_text);
        lv_obj_invalidate(ui.charged_label_1);  // 强制刷新显示
        NRF_LOG_INFO("Updated charged screen battery level: %s", battery_text);
    }
}