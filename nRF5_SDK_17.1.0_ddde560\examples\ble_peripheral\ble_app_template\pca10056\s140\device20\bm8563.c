#include "bm8563.h"
//#include "..\..\..\components\drivers_nrf\twi_master\deprecated\twi_master.h"
#include "twi_master.h"
//FW\source\driver
//
#include "nrf_gpio.h"
#include "app_error.h"
#include <stdio.h>
#include <stdlib.h>

typedef struct {
    uint8_t         addr;      /**< Instance index. */
    uint8_t         val; /**< Number of capture/compare channels. */
} BM8563_Init_t;
const uint8_t BM8563_CTR_STATUS_ADDR_1 = 0x00;
const uint8_t BM8563_CTR_STATUS_ADDR_2 = 0x01;
const uint8_t BM8563_DATA_START_ADDR   = 0x02;
const uint8_t BM8563_CLOCKOUT = 0x0D;


//I2C 读写函数会右移1位
/*时间戳转换为标准时间*/

const char Days[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};


uint8_t twdata[8]= {BM8563_DATA_START_ADDR,0x50,0x59,0x23,0x31,0x06,0x12,0x04}; /*用来设置时间寄存器 */
uint8_t trdata[7]= {0}; /*定义数组用来存储读取的时间数据 */


#define LEAP_YEAR_DAYS          366
#define NONLEAP_YEAR_DAYS       365
#define FOUR_YEAR_DAYS          1461U
#define FOUR_YEAR_HOURS         35064UL    /* 1461L * 24L */
#define MAX_SEC_PER_MINUTE      60
#define MAX_MIN_PER_HOUR        60
#define MAX_HOURS_PER_NONLEAP   8760UL     /* 365 * 24 */
#define MAX_SEC_PER_DAY         86400UL    /* 24 * 3600 */
#define MAX_SEC_PER_NONLEAP     31536000UL /* 365 * 24 * 3600 */

// 平年和闰年每月的天数
const unsigned monDays[2][12] = {
    {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31},
    {31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}
};
// 平年和闰年当前月份已过去的天数
//static uint16_t monYearDays[2][12] =
//{
//    {0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334},
//    {0, 31, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335},
//};

bool IsLeapYear(uint32_t year)
{
    return ((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0);
}



void LocalTime(uint32_t time,Tm *tm)
{
    tm->tm_sec = (unsigned char )(time % MAX_SEC_PER_MINUTE); //取秒时间
    uint32_t mins = time / MAX_SEC_PER_MINUTE;
    tm->tm_min = (unsigned char)(mins % MAX_MIN_PER_HOUR);  //取分钟时间

    uint32_t hours = mins / MAX_MIN_PER_HOUR;
    uint32_t pass4year = hours / FOUR_YEAR_HOURS;     //取过去多少个四年，每四年有 1461*24小时
    tm->tm_year = (pass4year << 2) + 1970;

    uint32_t fourYearRemainHours = hours % FOUR_YEAR_HOURS; // 四年中剩下的小时数
    while(1) { // 校正闰年影响的年份，计算一年中剩下的小时数
        uint32_t hoursPerYear = MAX_HOURS_PER_NONLEAP;
        if(tm->tm_year % 4 == 0) hoursPerYear += 24; //判断闰年 是闰年，一年则多24小时，即一天
        if (fourYearRemainHours < hoursPerYear) break;
        tm->tm_year++;
        fourYearRemainHours -= hoursPerYear;
    }
    tm->tm_hour = (unsigned char)(fourYearRemainHours % 24);
    // 一年中剩下的天数,一年中剩余的小时数/24=剩余天数
    uint32_t yearRemaindays = fourYearRemainHours / 24;
    // 修正今天未满一整天也算一天
    if(tm->tm_hour != 0 || tm->tm_min != 0 || tm->tm_sec != 0) yearRemaindays += 1;

    //const unsigned char *mon = monDays[IsLeapYear(tm->tm_year)];
    unsigned char xx = IsLeapYear(tm->tm_year);
    for (tm->tm_mon = 0; monDays[xx][tm->tm_mon] < yearRemaindays; tm->tm_mon++) {
        yearRemaindays -= monDays[xx][tm->tm_mon];
    }
    tm->tm_mday = (unsigned char)(yearRemaindays);
    tm->tm_wday =	(unsigned char)(yearRemaindays +3)%7;
    return;
}




//volatile uint8_t ack = 0;
//volatile uint8_t bm_status = 0;//如果BM时钟芯片无应答，则为0,。应答正常则为1.

/********************************************************************
函 数 名：unsigned char HexToBcd(unsigned char hexData)
功 能：      hex格式转bcd格式 供RTC使用
说 明：
调 用：
入口参数：1byte  16进制数
返 回 值：转换后的BCD码
***********************************************************************/
unsigned char HexToBcd(unsigned char hexData)
{
    unsigned char bcdData;
    bcdData = ((hexData / 10) << 4) + (hexData % 10);
    return bcdData;
}


/********************************************************************
函 数 名：unsigned char BcdToHex(unsigned char bcdData)
功 能：      bcd格式转hex格式
说 明：
调 用：
入口参数：2位bcd码
返 回 值：1byte16进制数
***********************************************************************/

unsigned char BcdToHex(unsigned char bcdData)
{
    unsigned char hexData;
    hexData = (bcdData >> 4) * 10 + (bcdData & 0x0F);
    return hexData;
}


/********************************************************************
函 数 名：datajust(void)
功 能：将读出的时间数据的无关位屏蔽掉
说 明：BM8563 时钟寄存器中有些是无关位，可以将无效位屏蔽掉
调 用：
入口参数：
返 回 值：无
***********************************************************************/
void datajust(void)
{
    trdata[0] = trdata[0]&0x7f;
    trdata[1] = trdata[1]&0x7f;
    trdata[2] = trdata[2]&0x3f;
    trdata[3] = trdata[3]&0x3f;
    trdata[4] = trdata[4]&0x07;
    trdata[5] = trdata[5]&0x1f;
    trdata[6] = trdata[6]&0xff;
}

uint8_t  IICInint(void)
{
    return(twi_master_init());
}


uint8_t BM8563Init(void)
{
    BM8563_Init_t tmp;
    tmp.addr = BM8563_CTR_STATUS_ADDR_1;
    tmp.val = 0;
    twi_master_transfer(BM8563ADDR,(uint8_t*)&tmp,2,TWI_ISSUE_STOP);

    tmp.addr = BM8563_CTR_STATUS_ADDR_2;
    tmp.val = 0;
    twi_master_transfer(BM8563ADDR,(uint8_t*)&tmp,2,TWI_ISSUE_STOP);

    tmp.addr = BM8563_CLOCKOUT;
    tmp.val = 0;
    twi_master_transfer(BM8563ADDR,(uint8_t*)&tmp,2,TWI_ISSUE_STOP);
    return 1;
}
/********************************************************************
函 数 名： GetBM8563(void)
功 能：从 BM8563 的内部寄存器（时间、状态、报警等寄存器）读取数据
说 明：该程序函数用来读取 BM8563 的内部寄存器，譬如时间，报警，状态等寄存器
采用页写的方式，设置数据的个数为 no，no 参数设置为 1 就是单字节方式
调 用：Start_I2C()，SendByte()，RcvByte()，Ack_I2C()，Stop_I2C()
入口参数：suba（BM8563 内部寄存器地址）
*s（设置读取数据存储的指针）， no（传输数据的个数）
返 回 值：有，用来鉴定传输成功否
***********************************************************************/


uint8_t GetBM8563(uint8_t suba,uint8_t *s,uint8_t no)
{
    BM8563_Init_t tmp;
    tmp.addr = suba;
    tmp.val = 0;
    if(twi_master_transfer(BM8563ADDR,(uint8_t*)&tmp,1,TWI_ISSUE_STOP)) {
        for(int i = 0; i < 7; i++) {
            trdata[i] = 0;
        }
        if (twi_master_transfer(BM8563ADDR | TWI_READ_BIT, trdata, no, TWI_DONT_ISSUE_STOP)) { // Read: current configuration
            for(int i = 0; i < 7; i++) {
                s[i] = trdata[i];
            }
            // Read succeeded, configuration stored to variable "config"
        } else {
            // Read failed

        }
    }


    return(1);
}
uint32_t  GetTimeStamp(void)
{
    BM8563_Init_t tmp;
    Times mytime;
    uint32_t timestamp = 0;
    tmp.addr = 0x02;
    tmp.val = 0;
    IICInint();
    if(twi_master_transfer(BM8563ADDR,(uint8_t*)&tmp,1,TWI_ISSUE_STOP)) {
        for(int i = 0; i < 7; i++) {
            trdata[i] = 0;
        }
        if (twi_master_transfer(BM8563ADDR | TWI_READ_BIT, trdata, 7, TWI_DONT_ISSUE_STOP)) { // Read: current configuration
            //	NRF_LOG_INFO("RTC Read OK");
            // Read succeeded, configuration stored to variable "config"
        } else {
            //NRF_LOG_INFO("RTC Read ERROR");
            return 0xffffffff;
            // Read failed
        }
    }
    datajust();
    mytime.Year = BcdToHex(trdata[6]) + 2000;
    mytime.Mon = BcdToHex(trdata[5]);
    mytime.Day = BcdToHex(trdata[3]);
    mytime.Hour = BcdToHex(trdata[2]);
    mytime.Min =  BcdToHex(trdata[1]);
    mytime.Second = BcdToHex(trdata[0]);
//	NRF_LOG_INFO("y=%d m =%d d=%d h=%d m =%d s=%d",mytime.Year,mytime.Mon,mytime.Day,mytime.Hour,mytime.Min,mytime.Second);
//	NRF_LOG_PROCESS();
    timestamp =(uint32_t)mktime(mytime.Year,mytime.Mon,mytime.Day,mytime.Hour,mytime.Min,mytime.Second);
    return timestamp;
}

/********************************************************************
函 数 名：SetBM8563(void)
功 能：设置 BM8563 的内部寄存器（时间，报警等寄存器）
说 明：该程序函数用来设置 BM8563 的内部寄存器，譬如时间，报警，状态等寄存器
采用页写的方式，设置数据的个数为 no，no 参数设置为 1 就是单字节方式
调 用：Start_I2C()，SendByte()，Stop_I2C()
入口参数：sla（BM8563 从地址）， suba（BM8563 内部寄存器地址）
*s（设置初始化数据的指针）， no（传输数据的个数）
返 回 值：有，用来鉴定传输成功否
***********************************************************************/
uint8_t SetBM8563(uint8_t suba,uint8_t *s,uint8_t no)   //no fixed 8
{
    twdata[0] = suba;
    for(int i=0; i<7; i++) {
        twdata[i+1] = s[i];
    }
    if (twi_master_transfer(BM8563ADDR, twdata, no, TWI_ISSUE_STOP)) { // Read: current configuration
        return 1;
        // Read succeeded, configuration stored to variable "config"
    } else {
        return 0;
        // Read failed

    }


}
uint8_t HEXtoBcd(uint8_t HEX)
{
    uint8_t tempData;

    tempData = ((HEX / 10) << 0x04) + (HEX % 10);
    return (tempData);
}

bool SetTimeFromStamp(uint32_t mstamp,uint32_t mzone, uint32_t wday)
{
    Tm mytime;
    IICInint();
    BM8563Init();
    LocalTime(mstamp,&mytime);
    twdata[0] = 0x02;
    twdata[1] = HexToBcd(mytime.tm_sec);
    twdata[2] = HexToBcd(mytime.tm_min);
    twdata[3] = HexToBcd((mytime.tm_hour+8)%24);
    twdata[4] = HexToBcd(mytime.tm_mday);
    //twdata[5] = HexToBcd(mytime.tm_wday);
    twdata[5] = HexToBcd(wday);
    twdata[6] = HexToBcd(mytime.tm_mon+1);
    twdata[7] = HexToBcd(mytime.tm_year - 2000);
    //NRF_LOG_INFO("st=%d",mstamp);
    NRF_LOG_INFO("ts=%x tm =%x th=%x td=%x tm =%x ty=%x",twdata[1],twdata[2],twdata[3],twdata[4],twdata[6],twdata[7]);
    NRF_LOG_PROCESS();
    if(twi_master_transfer(BM8563ADDR, (unsigned char*)&twdata, 8, TWI_ISSUE_STOP)) {
        return true;
    } else {
        return false;
    }
}





/********************************************************************
函 数 名：void Bcd2asc(void)
功 能：bcd 码转换成 asc 码，供液晶显示用
说 明：
调 用：
入口参数：
返 回 值：无
***********************************************************************/
void Bcd2asc(unsigned char*val)
{

}


/********************************************************************
函 数 名：Set_Start_BM8563(void)
功 能：配置启动BM8563
说 明：
调 用：
入口参数：
返 回 值：无
***********************************************************************/

void Set_Start_BM8563(void)
{

    //uint8_t bm_status = SetBM8563(0xa2,0x00,twdata,0x09);//设置时间和日期

}

/********************************************************************
函 数 名：unsigned long mktime(const unsigned int year0, const unsigned int mon0,
       const unsigned int day, const unsigned int hour,
       const unsigned int min, const unsigned int sec)
功 能：时间结构体转换成时间戳
说 明：
调 用：
入口参数：
返 回 值：无
***********************************************************************/


unsigned long mktime(const unsigned int year0, const unsigned int mon0,
                     const unsigned int day, const unsigned int hour,
                     const unsigned int min, const unsigned int sec)
{
    unsigned int mon = mon0, year = year0;
    if (0 >= (int) (mon -= 2)) {
        mon += 12;	/* Puts Feb last since it has leap day */
        year -= 1;
    }

    return ((((unsigned long)
              (year/4 - year/100 + year/400 + 367*mon/12 + day) +
              year*365 - 719499                                   //-480  消除时区
             )*24 + hour /* now have hours */
            )*60 + min /* now have minutes */
           )*60 + sec - 28800; /* finally seconds  - 28800  消除时区影响*/

}

#if 0
/*标准时间转换为时间戳*/
int standard_to_stamp(char *str_time)
{
    struct tm stm;
    int iY,iM,iD,iH,iMin,iS;
    memset(&stm,0,sizeof(stm));
    iY = atoi(str_time);
    iM = atoi(str_time+5);     //和实际输入的格式 有关系
    iD = atoi(str_time+7);
    iH = atoi(str_time+10);
    iMin = atoi(str_time+13);
    iS = atoi(str_time+16);
    stm.tm_year=iY-1900;
    stm.tm_mon=iM-1;
    stm.tm_mday=iD;
    stm.tm_hour=iH;
    stm.tm_min=iMin;
    stm.tm_sec=iS;
    //printf("解析后的时间是%d-%d-%d %d:%d:%d\n", iY, iM, iD, iH, iMin, iS);

    return (int)mktime(&stm);

}
#endif

#if 0
Times stamp_to_standard(int stampTime)
{
    int tick = stampTime;
    struct tm tm;
    char s[100];
    Times standard;
    tm = *localtime(&tick);
    strftime(s, sizeof(s), "%Y-%m-%d %H:%M:%S", &tm);
    printf("时间戳为：%d 转换成标准时间为： %s\n", (int)tick, s);

    standard.Year = atoi(s);
    standard.Mon = atoi(s+5);
    standard.Day = atoi(s+8);
    standard.Hour = atoi(s+11);
    standard.Min = atoi(s+14);
    standard.Second = atoi(s+17);

    return standard;
}
#endif


#if 0
int main(int argc, char **argv)
{
    int a =0;

    printf("输入的标准时间是: %s \n",argv[1]);

    a=standard_to_stamp(argv[1]);
    printf("标准时间转换为时间戳: %d\n",a);

    stamp_to_standard(a);

    return  0;
}
#endif

/********************************************************************
测试代码
***********************************************************************/
#if 0
int main(void)
{
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART3_UART_Init();
    HAL_Delay(1000);
    Set_Start_BM8563();	//设置时间、日期
    while (1) {
        HAL_Delay(1000);
        HAL_GPIO_TogglePin(LED_GPIO_Port,LED_Pin);
        do {
            bm_status=GetBM8563(0xa2,0x02,trdata,0x07);//测试读取时间、日期
        } while(bm_status==0);
        datajust();
        Bcd2asc();
        printf_sz_hex(asc,14);	//打印时间、日期
    }
}
#endif


