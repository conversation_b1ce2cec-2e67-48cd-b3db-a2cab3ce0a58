#include "watchdog.h"
#include "nrf_drv_wdt.h"
#include "nrf_drv_clock.h"


#define WATCHDOG_RESET_TIME_MS  (50L)


#ifndef DEBUG
nrf_drv_wdt_channel_id m_channel_id;
APP_TIMER_DEF(watchdog_timer_id);
#endif // DEBUG


#ifndef DEBUG
//看门狗触发
static void watchdog_event_handler(void)
{
    //NOTE: The max amount of time we can spend in WDT interrupt is two cycles of 32768[Hz] clock - after that, reset occurs
    NRF_LOG_ERROR("wdt_event_handler");
}
#endif // DEBUG


//喂狗
void watchdog_feed(void)
{
#ifndef DEBUG
    nrf_drv_wdt_channel_feed(m_channel_id);
#endif // DEBUG
}


#ifndef DEBUG
//定时器回调
static void watchdog_timeout_handler(void * p_context)
{
    watchdog_feed();
}
#endif // DEBUG


//看门狗初始化
void watchdog_init(void)
{
#ifndef DEBUG
    APP_ERROR_CHECK_BOOL(WATCHDOG_RESET_TIME_MS >= 1 && WATCHDOG_RESET_TIME_MS <= 1000);

    ret_code_t ret_code;

    //打开低频时钟
    ret_code = nrf_drv_clock_init();
    if (ret_code != NRF_ERROR_MODULE_ALREADY_INITIALIZED) { //正常的话应该在协议栈初始化中做过了
        APP_ERROR_CHECK(ret_code);
    }
    nrf_drv_clock_lfclk_request(NULL);

    //配置看门狗
    nrf_drv_wdt_config_t config = NRF_DRV_WDT_DEAFULT_CONFIG;
    config.reload_value = WATCHDOG_RESET_TIME_MS;
    ret_code = nrf_drv_wdt_init(&config, watchdog_event_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = nrf_drv_wdt_channel_alloc(&m_channel_id);
    APP_ERROR_CHECK(ret_code);
    nrf_drv_wdt_enable();
    watchdog_feed();

    //周期喂狗定时器
    ret_code = app_timer_create(&watchdog_timer_id, APP_TIMER_MODE_REPEATED, watchdog_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(watchdog_timer_id, APP_TIMER_TICKS(10), NULL);
    APP_ERROR_CHECK(ret_code);
#endif // DEBUG
}



