#include "light.h"
#include "saadc.h"
#include "kalman_filter.h"
#include "led_state.h"
#include "led_array.h"
#include "frame.h"
#include "tm1640.h"


#define PWM_LED_ARRAY_MIN_DUTY    (3)
#define PWM_LED_ARRAY_MAX_DUTY    (100)
#define PWM_LED_STATE_MIN_DUTY    (1)
#define PWM_LED_STATE_MAX_DUTY    (20)



APP_TIMER_DEF(light_timer_id);
static kalman1_state light_kalman_state;
static uint32_t light_vol_mv = 0xFFFFFFFF;



//动态调整点阵亮度
static void light_adjust_led_array_bright(void)
{


#if 0


    if (duty_led_array < PWM_LED_ARRAY_MIN_DUTY)
        duty_led_array = PWM_LED_ARRAY_MIN_DUTY;
    if (duty_led_array > PWM_LED_ARRAY_MAX_DUTY)
        duty_led_array = PWM_LED_ARRAY_MAX_DUTY;
    led_array_set_bright(duty_led_array);
#endif
    uint32_t duty_led_state = light_vol_mv / 50;
    if (duty_led_state < PWM_LED_STATE_MIN_DUTY)
        duty_led_state = PWM_LED_STATE_MIN_DUTY;
    if (duty_led_state > PWM_LED_STATE_MAX_DUTY)
        duty_led_state = PWM_LED_STATE_MAX_DUTY;
    led_state_set_bright(duty_led_state);
    uint32_t  led_matrix_bri  = light_vol_mv / 80;
    if (led_matrix_bri < 1)
        led_matrix_bri = 1;
    if (led_matrix_bri > 3)
        led_matrix_bri = 3;
    tm1640_brighteness(led_matrix_bri);

#if 0
    NRF_LOG_INFO("light_vol_mv = %d, duty_led_array = %d, duty_led_state = %d", light_vol_mv, duty_led_array, duty_led_state);

//发送环境光强数据到APP
    static uint32_t n = 0;
    if ((++n % 100) == 0) {
        char str[100];
        snprintf(str, sizeof(str), "light_vol_mv=%d, duty_led_array=%d, duty_led_state=%d", light_vol_mv, duty_led_array, duty_led_state);
        frame_send_string(str);
    }
#endif
}


//定时器回调
static void light_timeout_handler(void * p_context)
{
    UNUSED_PARAMETER(p_context);
    uint32_t vol_mv;

    vol_mv = saadc_convert(SAADC_CHANNEL_LIGHT, 1);

    if (light_vol_mv == 0xFFFFFFFF) {
        kalman1_init(&light_kalman_state, vol_mv, 50, 1e2, 5e5);
        light_vol_mv = vol_mv;
    } else {
        light_vol_mv = kalman1_filter(&light_kalman_state, vol_mv);
    }

//   NRF_LOG_INFO("vol_mv = %4d, vol_mv_filtered = %4d", vol_mv, light_vol_mv);

    //动态调整点阵亮度
    light_adjust_led_array_bright();
}


//环境光强度检测初始化
void light_init(void)
{
    ret_code_t ret_code;

    ret_code = app_timer_create(&light_timer_id, APP_TIMER_MODE_REPEATED, light_timeout_handler);
    APP_ERROR_CHECK(ret_code);

    ret_code = app_timer_start(light_timer_id, APP_TIMER_TICKS(10), NULL);
    APP_ERROR_CHECK(ret_code);
}


//关闭环境光检测，并用最亮的值
void light_deinit(void)
{
    ret_code_t ret_code = app_timer_stop(light_timer_id);
    APP_ERROR_CHECK(ret_code);

    led_array_set_bright(PWM_LED_ARRAY_MAX_DUTY);
    led_state_set_bright(PWM_LED_STATE_MAX_DUTY);
}
