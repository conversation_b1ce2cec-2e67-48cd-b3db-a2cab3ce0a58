#include "main.h"
#include "spiflash20.h"
#include "nrf_nvic.h"

//指令表

#define W25X_WriteEnable		  	0x06
#define W25X_WriteDisable		  	0x04
#define W25X_ReadStatusReg		  0x05
#define W25X_WriteStatusReg		  0x01
#define W25X_ReadData			  		0x03
#define W25X_FastReadData		  	0x0B
#define W25X_FastReadDual		  	0x3B
#define W25X_PageProgram		  	0x02
#define W25X_BlockErase			  	0xD8
#define W25X_SectorErase		  	0x20
#define W25X_ChipErase			  	0xC7
#define W25X_PowerDown			  	0xB9
#define W25X_ReleasePowerDown	  0xAB
#define W25X_DeviceID			  		0xAB
#define W25X_ManufactDeviceID	  0x90
#define W25X_JedecDeviceID		  0x9F   //9F    
#define FLASH_ID 0XBA6012  //器件ID
#define Dummy_Byte 0XFF
#define WIP_Flag  0x01


//芯片信息
#define FLASHPAGESIZE (256)
#define FALSHSECTORSIZE (4096)
#define FLASHBLOCKSIZE  (64*1024)

//注意，网上资料nordic不能一次读\写 256字节，最多只能写251个，所以一页要分2次写

#define USER_SPI_CONFIG_IRQ_PRIORITY_HIGH 3

#define USRE_NRF_DRV_SPI_FLSH_CONFIG                         \
{                                                            \
    .sck_pin      = SPI_SCK_PIN,                            \
    .mosi_pin     = SPI_MOSI_PIN,                           \
    .miso_pin     = SPI_MISO_PIN,                           \
    .ss_pin       = NRF_DRV_SPI_PIN_NOT_USED,                \
    .irq_priority = USER_SPI_CONFIG_IRQ_PRIORITY_HIGH,         \
    .orc          = 0xFF,                                    \
    .frequency    = NRF_DRV_SPI_FREQ_500K,                   \
    .mode         = NRF_DRV_SPI_MODE_0,                      \
    .bit_order    = NRF_DRV_SPI_BIT_ORDER_MSB_FIRST,         \
}
#define SPI_BUFSIZE 16 //SPI缓存的大小


static const nrf_drv_spi_t spi = NRF_DRV_SPI_INSTANCE(2);  /**< SPI instance. */
static volatile bool spi_xfer_done;  /**< Flag used to indicate that SPI instance completed the transfer. */
volatile  uint8_t   SPIReadLength, SPIWriteLength;
uint8_t   SPI_Tx_Buf[SPI_BUFSIZE];  //发送
uint8_t   SPI_Rx_Buf[SPI_BUFSIZE];  //接收

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context)
{
    spi_xfer_done = true;
}

void spi_master_init(void)
{
    nrf_drv_spi_config_t spi_config = USRE_NRF_DRV_SPI_FLSH_CONFIG;
    spi_config.ss_pin   = NRF_DRV_SPI_PIN_NOT_USED;
    APP_ERROR_CHECK(nrf_drv_spi_init(&spi, &spi_config, spi_event_handler, NULL));
}




/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data)
{
    spi_xfer_done = false;
    SPIWriteLength = 1;
    SPIReadLength = 0;
    SPI_Tx_Buf[0] = data;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, SPIWriteLength, SPI_Rx_Buf, SPIReadLength));
    while(spi_xfer_done == false);
}
/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg)
{
    spi_xfer_done = false;
    SPI_Tx_Buf[0] = reg;
    APP_ERROR_CHECK(nrf_drv_spi_transfer(&spi, SPI_Tx_Buf, 0, SPI_Rx_Buf,1));
    while(spi_xfer_done == false);
    return SPI_Rx_Buf[0];
}

/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void)
{
    uint32_t temp = 0,temp0 = 0,temp1 = 0,temp2 = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_JedecDeviceID);
    temp0 = spi_flash_read_reg(0XFF);
    temp1 = spi_flash_read_reg(0XFF);
    temp2 = spi_flash_read_reg(0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    temp = (temp0 << 16)| (temp1 << 8) | temp2;
    return temp;
}

/*
写使能命令
*/
void spi_flash_WriteEnable(void)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_WriteEnable);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选有效
}

/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void)
{
    unsigned char FLASH_Status = 0;
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadStatusReg); //发送读状态寄存器
    do {
        FLASH_Status = spi_flash_read_reg(Dummy_Byte);
    } while((WIP_Flag & FLASH_Status) == 1);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效

}
/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    spi_flash_WaitForWriteEnd();  //等待写完成
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_SectorErase);  //发送扇区擦除指令
    spi_flash_write_reg((SectorAddr & 0XFF0000) >> 16); //发送扇区擦除地址的高位
    spi_flash_write_reg((SectorAddr & 0XFF00) >> 8);
    spi_flash_write_reg(SectorAddr & 0XFF);
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待擦除完成

    uint8_t check_buf[4];
    spi_flash_Flash_BufferRead(check_buf, SectorAddr, 4);
    if((check_buf[0] != 0xFF) || (check_buf[1] != 0xFF) ||
       (check_buf[2] != 0xFF) || (check_buf[3] != 0xFF)) {
        // NRF_LOG_ERROR("Sector erase failed!");
        NRF_LOG_INFO("Reset");
        nrf_delay_ms(100);
        NVIC_SystemReset();
    }
}

/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_PageProgram);  //发送写指令
    spi_flash_write_reg((WriteAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((WriteAddr & 0XFF00) >> 8);
    spi_flash_write_reg(WriteAddr & 0XFF);
    if(NumByteToWrite > 256) {
        NRF_LOG_INFO("write too large!\r\n");
        NRF_LOG_PROCESS();
        return ;
    }
    while(NumByteToWrite--) {
        spi_flash_write_reg(*pBuffer);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}
/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead)
{
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ReadData);  //发送写指令
    spi_flash_write_reg((ReadAddr & 0XFF0000) >> 16); //发送写地址的高位
    spi_flash_write_reg((ReadAddr & 0XFF00) >> 8);
    spi_flash_write_reg(ReadAddr & 0XFF);
    while(NumByteToRead--) {
        *pBuffer = spi_flash_read_reg(Dummy_Byte);
        pBuffer++;
    }
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
}
/*
全片擦除
*/
void spi_flash_Chip_Erase(void)
{
    spi_flash_WriteEnable();  //发送FLASH写使能命令
    nrf_gpio_pin_clear(SPI_SS_PIN);  //片选有效
    spi_flash_write_reg(W25X_ChipErase);  //全片擦除
    nrf_gpio_pin_set(SPI_SS_PIN);  //片选无效
    spi_flash_WaitForWriteEnd();  //等待写完成
}


void spi_flash_init(void)
{
    uint32_t ID = 0;
    ID = spi_flash_ReadID();
    NRF_LOG_INFO("flash ID %d\r\n",ID);
//    if(ID != FLASH_ID)
//    {
//        NRF_LOG_INFO("init w25q80 error\r\n");
//
//    }
//    else
//    {
//        NRF_LOG_INFO("init w25q80 ok!\r\n");
//        NRF_LOG_INFO("FLASH ID is %X",ID);
//    }
}



