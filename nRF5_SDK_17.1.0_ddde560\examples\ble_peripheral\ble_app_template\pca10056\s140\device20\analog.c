#include "analog.h"
#include "saadc.h"
#include "pwm.h"
#include "power.h"
#include "sm.h"
#include "utils.h"
//#include "factory.h"
#include "led_state.h"
#include "storage.h"
#include "twiTMP.h"


/*
时序
波形：    __________ ---------- ----------
颜色：       DARK      Y-20mA     G-20mA
时长ms：   20 + 50     20 + 50    20 + 50
实测：绿灯亮时电池功耗：250mA@3V，蓝灯亮时电池功耗：500mA@3V
*/


//1号机
//#define LED_GREEN_CUR      (64)  //50    //70 OK
//#define LED_BLUE_CUR        (72)  //18    //80 OK

//2号机
#define LED_GREEN_CUR      (60)  //50    //70 OK
#define LED_BLUE_CUR        (77)  //18    //80 OK

APP_TIMER_DEF(led_timer_id);
static bool m_is_analog_working = false;
extern bool bat_low_power;
//逻辑状态机
static enum {
    ANALOG_IDLE = 0,    //灯全不亮
    ANALOG_DARK,        //灯全不亮
    ANALOG_GREEN,       //只有黄灯亮
    ANALOG_BLUE,        //只有蓝灯亮
} analog_sm = ANALOG_IDLE;

static uint32_t m_is_amp_3;
static float real_tempture;
static cali_para_t acali_raw_para;

//+++++++++++++++++++++++++++++++++++++++=divid
//绿光开关控制
static void led_green_switch(bool onoff)
{
    if (onoff) {
        nrf_gpio_pin_clear(LED_B_CUT_PIN);
        nrf_gpio_pin_set(LED_G_CUT_PIN);
        // nrf_gpio_cfg_input(LED_G_CUT_PIN, NRF_GPIO_PIN_NOPULL);
    } else {

        nrf_gpio_pin_clear(LED_G_CUT_PIN);
        // nrf_gpio_pin_write(LED_G_CUT_PIN, 0);
        // nrf_gpio_cfg_output(LED_G_CUT_PIN);
    }
}


//蓝光开关控制
static void led_blue_switch(bool onoff)
{
    if (onoff) {
        nrf_gpio_pin_clear(LED_G_CUT_PIN);
        nrf_gpio_pin_set(LED_B_CUT_PIN);

        // nrf_gpio_cfg_input(LED_B_CUT_PIN, NRF_GPIO_PIN_NOPULL);
    } else {
        nrf_gpio_pin_clear(LED_B_CUT_PIN);
        // nrf_gpio_pin_write(LED_B_CUT_PIN, 0);
        // nrf_gpio_cfg_output(LED_B_CUT_PIN);
    }
}

//关闭所有灯
static void led_off_switch()
{

    nrf_gpio_pin_clear(LED_B_CUT_PIN);
    nrf_gpio_pin_clear(LED_G_CUT_PIN);

}
volatile static uint32_t adc_amp1 =0;
//倒计时定时器
static void led_timeout_handler(void * p_context)
{
    UNUSED_PARAMETER(p_context);
    bool err;
#define RAW_DATA_CNT    (100)           //原始数据数组长度

    ret_code_t ret_code;
    static uint32_t time_passed_ms = 0;     //用于时序计时
    static uint32_t m_raw_data_buf[RAW_DATA_CNT];    //原始数据
    volatile static uint32_t m_raw_data_buf_cnt;     //原始数据个数
    static uint32_t vol_dark = 0;           //不发光时的电压
    static uint32_t vol_green = 0;         //只有绿灯发光时的电压
    static uint32_t vol_blue = 0;           //只有蓝灯发光时的电压
    uint32_t duty_green = 0;
    uint32_t duty_blue = 0;

#ifdef	FIXEDCALDATA
    //样机2

    duty_green = 50;
    duty_blue = 50;
#else
//	storage_read_duty(&duty_green,&duty_blue);   //divid debug
    //	NRF_LOG_INFO("Gduty=%d,Bduty=%d",duty_green,duty_blue);
    //	NRF_LOG_PROCESS();
#endif
    //PD电流经IV转换和放大后采样
    adc_amp1 = saadc_convert(SAADC_CHANNEL_AMP1, 5);
    uint32_t adc_amp2 = saadc_convert(SAADC_CHANNEL_AMP2, 5);
//	第一次调试

    //状态机
    time_passed_ms += 1;

    if (analog_sm == ANALOG_IDLE) {
        led_off_switch();
        pwm_led_set_duty(0);//divid add
        time_passed_ms = 0;
        memset(m_raw_data_buf, 0, sizeof(m_raw_data_buf));
        m_raw_data_buf_cnt = 0;
        time_passed_ms = 0;
        analog_sm = ANALOG_DARK;
        m_is_amp_3 = false;
    } else if (analog_sm == ANALOG_DARK) {

        if (time_passed_ms <= 5) {
					//读取探头温度
            real_tempture = get_temperature();
            vol_dark = 0;
        } else if (time_passed_ms <= 20) {
            m_raw_data_buf[m_raw_data_buf_cnt++] = m_is_amp_3 ? adc_amp2 : adc_amp1;
            APP_ERROR_CHECK_BOOL(m_raw_data_buf_cnt <= RAW_DATA_CNT);

        } else {
            //中值滤波
            vol_dark = median_filter(m_raw_data_buf, m_raw_data_buf_cnt, 10);
            //点亮绿灯（只有当duty_green > 0时才点亮）
            if (duty_green > 0) {
                led_green_switch(true);        //切换开关
                if(!bat_low_power) {
                    pwm_led_set_duty(duty_green);
                }
            } else {
                led_green_switch(false);  // duty_green为0时关闭绿灯
                pwm_led_set_duty(0);      // 确保PWM占空比为0
            }
            time_passed_ms = 0;
            memset(m_raw_data_buf, 0, sizeof(m_raw_data_buf));
            m_raw_data_buf_cnt = 0;
            analog_sm = ANALOG_GREEN;
            m_is_amp_3 = true;
        }
    } else if (analog_sm == ANALOG_GREEN) {

        if (time_passed_ms <= 5) {
            vol_green = 0;
        } else if (time_passed_ms <= 20) {
            m_raw_data_buf[m_raw_data_buf_cnt++] = adc_amp2;
            APP_ERROR_CHECK_BOOL(m_raw_data_buf_cnt <= RAW_DATA_CNT);
        } else {
            //中值滤波
            vol_green = median_filter(m_raw_data_buf, m_raw_data_buf_cnt, 10);
            //点亮蓝灯（只有当duty_blue > 0时才点亮）
            if (duty_blue > 0) {
                led_blue_switch(true);
                if(!bat_low_power) {
                    pwm_led_set_duty(duty_blue);
                }
            } else {
                led_blue_switch(false);  // duty_blue为0时关闭蓝灯
                pwm_led_set_duty(0);     // 确保PWM占空比为0
            }
            time_passed_ms = 0;
            memset(m_raw_data_buf, 0, sizeof(m_raw_data_buf));
            m_raw_data_buf_cnt = 0;
            analog_sm = ANALOG_BLUE;
            m_is_amp_3 = false;
        }
    } else if (analog_sm == ANALOG_BLUE) {

        if (time_passed_ms <= 5) {
            vol_blue = 0;
        } else if (time_passed_ms <= 20) {
            m_raw_data_buf[m_raw_data_buf_cnt++] = adc_amp1;
            APP_ERROR_CHECK_BOOL(m_raw_data_buf_cnt <= RAW_DATA_CNT);
        } else {
            //停止定时器
            ret_code = app_timer_stop(led_timer_id);
            APP_ERROR_CHECK(ret_code);
            pwm_led_set_duty(0);
            led_off_switch();
            //检测完成
            m_is_analog_working = false;
            analog_sm = ANALOG_IDLE;
            //运放输出电压转换为PD输出电流
            vol_blue = median_filter(m_raw_data_buf, m_raw_data_buf_cnt, 10);//中值滤波
            uint32_t dark = vol_dark;
            uint32_t green = vol_green;
            uint32_t blue = vol_blue;

            //上报事件
            ui_evt_t ui_evt;
            ui_evt.ui_evt_type = UI_EVT_TYPE_MEASURE_DONE;
            ui_evt.evt.ui_evt_measure_done.dark = dark;
            ui_evt.evt.ui_evt_measure_done.green = green;
            ui_evt.evt.ui_evt_measure_done.blue = blue;
            ui_evt.evt.ui_evt_measure_done.tempture = real_tempture;
            sm_event(ui_evt);
        }

    }
}


//模拟电路控制初始化
void analog_init(void)
{
    pwm_led_set_duty(0);
    led_green_switch(false);
    led_blue_switch(false);
//	storage_read_cali_para(&acali_raw_para);
    ret_code_t ret_code = app_timer_create(&led_timer_id, APP_TIMER_MODE_REPEATED, led_timeout_handler);
    APP_ERROR_CHECK(ret_code);
}


//模拟电路控制反初始化
void analog_deinit(void)
{
    nrf_gpio_cfg_input(LED_G_CUT_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_B_CUT_PIN, NRF_GPIO_PIN_NOPULL);
}


//触发一次采样
//is_amp_3：是否放大3倍
void analog_measure(uint32_t is_amp_3)
{
    ret_code_t ret_code;

    if (m_is_analog_working) {
        return;
    }
    is_amp_3 = is_amp_3;
    m_is_analog_working = true;		//需要在测量结束写为false
    // m_is_amp_3 = is_amp_3;
    ret_code = app_timer_stop(led_timer_id);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(led_timer_id, APP_TIMER_TICKS(1), NULL);
    APP_ERROR_CHECK(ret_code);
    analog_sm = ANALOG_IDLE;
}

