#include "spi.h"
//#include "power.h"

#include "nrf_drv_spi.h"


static const nrf_drv_spi_t spi_st7789 = NRF_DRV_SPI_INSTANCE(0);
static nrf_drv_spi_config_t spi_st7789_config = {
    .sck_pin  = GPIO_LCD_SCK,
    .mosi_pin = GPIO_LCD_MOSI,
    .miso_pin = NRF_DRV_SPI_PIN_NOT_USED,
    .ss_pin   = NRF_DRV_SPI_PIN_NOT_USED,
    .irq_priority = APP_IRQ_PRIORITY_MID,
    .orc          = 0xFF,
    .frequency    = NRF_DRV_SPI_FREQ_8M,
    .mode         = NRF_DRV_SPI_MODE_0,
    .bit_order    = NRF_DRV_SPI_BIT_ORDER_MSB_FIRST,
};
static volatile struct {
    bool is_spi_working;			//当前SPI传输是否在进行中
    uint32_t source_addr;			//剩余的要写入到SPI的源数据首地址
    uint32_t remain_len;			//剩余的要写入到SPI的源数据长度
    spi_dma_finish_cb_t finish_cb;	//完成时回调函数
} spi_st7789_status = {
    .is_spi_working = false,
    .finish_cb = NULL,
};


static void spi_st7789_write_chunk(void);


//SPI回调函数
static void spi_st7789_event_handler(nrf_drv_spi_evt_t const *p_event, void *p_context)
{
    if (p_event->type == NRF_DRV_SPI_EVENT_DONE) {
        spi_st7789_write_chunk();
    }
}

//SPI初始化
void spi_st7789_init(void)
{
    ret_code_t ret = nrf_drv_spi_init(&spi_st7789, &spi_st7789_config, spi_st7789_event_handler, NULL);
    APP_ERROR_CHECK(ret);
}

//SPI传输一批数据
//因为DMA最多只能一次传输255字节数据，可能要分多次DMA才能传输完成
static void spi_st7789_write_chunk(void)
{
    //数据已经都传输完了
    if (spi_st7789_status.remain_len == 0) {
        spi_st7789_status.is_spi_working = false;

        //回调
        if (spi_st7789_status.finish_cb)
            spi_st7789_status.finish_cb();
    }
    //还有数据需要传输
    else {
        uint8_t *data = (uint8_t*)spi_st7789_status.source_addr;
        uint32_t len = MIN(spi_st7789_status.remain_len, 255);//NRF52832的SPI的DMA模式，只支持8位长度的数据
        //log("len = %d", len);
        spi_st7789_status.source_addr += len;
        spi_st7789_status.remain_len -= len;

        //NRF52832的SPI的DMA模式，只支持8位长度的数据
        nrfx_spim_xfer_desc_t const spim_xfer_desc = {
            .p_tx_buffer = (uint8_t *)data,
            .tx_length   = len,
            .p_rx_buffer = NULL,//不接收
            .rx_length   = 0,
        };
        ret_code_t ret = nrfx_spim_xfer(&spi_st7789.u.spim, &spim_xfer_desc, 0);
        APP_ERROR_CHECK(ret);
    }
}

//SPI发送数据
void spi_st7789_send(uint8_t *data, uint32_t len, spi_dma_finish_cb_t spi_dma_finish_cb)
{
    //NRF_LOG_INFO("spi_st7789_send: 0x%0.8X, %d", (uint32_t)data, len);

    //如果有未完成的传输，则等待
    while (spi_st7789_status.is_spi_working) {
//		power_wait();
    }

    spi_st7789_status.finish_cb = spi_dma_finish_cb;

    //设置状态变量
    spi_st7789_status.is_spi_working = true;
    spi_st7789_status.source_addr = (uint32_t)data;
    spi_st7789_status.remain_len = len;

    //启动spi_st7789写入
    spi_st7789_write_chunk();

    //有回调函数，则直接返回；没有回调函数，则要等待执行完成
    if (spi_st7789_status.finish_cb == NULL) {
        //等待本次发送完成
        while (spi_st7789_status.is_spi_working);
    }
}
