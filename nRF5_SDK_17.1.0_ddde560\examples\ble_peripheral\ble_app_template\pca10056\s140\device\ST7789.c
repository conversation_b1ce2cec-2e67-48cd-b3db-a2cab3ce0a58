#include "st7789.h"
#include "spi.h"


static void st7789_send_finish_callback(void);
static void st7789_wr_cmd(uint8_t byte);
static void st7789_wr_data(uint8_t byte);
static void st7789_set_addr_range(int16_t x1, int16_t y1, int16_t x2, int16_t y2);


//st7789初始化
void st7789_init(void)
{
    //IO初始化
    nrf_gpio_cfg(
        GPIO_LCD_BK,
        NRF_GPIO_PIN_DIR_OUTPUT,
        NRF_GPIO_PIN_INPUT_DISCONNECT,
        NRF_GPIO_PIN_NOPULL,
        NRF_GPIO_PIN_H0H1,
        NRF_GPIO_PIN_NOSENSE);
    nrf_gpio_cfg_output(GPIO_LCD_CS);
    nrf_gpio_cfg_output(GPIO_LCD_RES);
    nrf_gpio_cfg_output(GPIO_LCD_DC);

    //SPI初始化
    spi_st7789_init();

    //reset
    nrf_gpio_pin_write(GPIO_LCD_RES, 0);
    nrf_delay_ms(1);
    nrf_gpio_pin_write(GPIO_LCD_RES, 1);
    nrf_delay_ms(120);

    //sleep out
    st7789_wr_cmd(0x11);
    nrf_delay_ms(120);

    //Tearing Effect Line On
    st7789_wr_cmd(0x35);
    st7789_wr_data(0x00);//The Tearing Effect output line consists of V-Blanking information only

    //Memory Data Access Control 内存写入顺序、颜色顺序等
    st7789_wr_cmd(0x36);
    //寄存器0x36的各数据位功能
    //bit7 Page Address Order, “0” = Top to Bottom, “1” = Bottom to Top
    //bit6 Column Address Order, “0” = Left to Right, “1” = Right to Left
    //bit5 Page/Column Order, “0” = Normal Mode, “1” = Reverse Mode
    //bit4 Line Address Order, “0” = LCD Refresh Top to Bottom, “1” = LCD Refresh Bottom to Top
    //bit3 RGB/BGR Order, “0” = RGB, “1” = BGR
    //bit2 Display Data Latch Data Order, “0” = LCD Refresh Left to Right, “1” = LCD Refresh Right to Left
    if (LCD_DIRECTION == 0)
        st7789_wr_data(0);
    else if (LCD_DIRECTION == 1)
        st7789_wr_data((1 << 7) | (1 << 6));
    else if (LCD_DIRECTION == 2)
        st7789_wr_data((1 << 6) | (1 << 5) | (1 << 4));
    else
        st7789_wr_data((1 << 7) | (1 << 5));

    //Interface Pixel Format 颜色格式
    st7789_wr_cmd(0x3A);
    st7789_wr_data(0x05);//RGB565, 16bit

    //porch 门廊
    st7789_wr_cmd(0xB2);
    st7789_wr_data(0x0C);
    st7789_wr_data(0x0C);
    st7789_wr_data(0x00);
    st7789_wr_data(0x33);
    st7789_wr_data(0x33);

    //Gate Control
    st7789_wr_cmd(0xB7);
    st7789_wr_data(0x35);//VGH=13.26V, VGL=-10.43V

    //VCOM Setting
    st7789_wr_cmd(0xBB);
    st7789_wr_data(0x1D);//VCOM=0.825V

    //LCM Control
    st7789_wr_cmd(0xC0);
    st7789_wr_data(0x2C);//XOR RGB setting in command 36h, XOR MX setting in command 36h

    //VDV and VRH Command Enable
    st7789_wr_cmd(0xC2);
    st7789_wr_data(0x01);

    //VRH Set
    st7789_wr_cmd(0xC3);
    st7789_wr_data(0x13);//VAP(GVDD)=4.5+( vcom+vcom offset+vdv)V

    //VDV Set
    st7789_wr_cmd(0xC4);
    st7789_wr_data(0x20);//VDV=0V

    //Frame Rate Control in Normal Mode
    st7789_wr_cmd(0xC6);
    st7789_wr_data(0x0F);//60Hz

    //Power Control 1
    st7789_wr_cmd(0xD0);
    st7789_wr_data(0xA4);
    st7789_wr_data(0xA1);//AVDD=6.8V, AVCL=-4.6V

    st7789_wr_cmd(0xD6);
    st7789_wr_data(0xA1);

    //Positive Voltage Gamma Control
    st7789_wr_cmd(0xE0);
    st7789_wr_data(0xF0);
    st7789_wr_data(0x00);
    st7789_wr_data(0x04);
    st7789_wr_data(0x04);
    st7789_wr_data(0x04);
    st7789_wr_data(0x05);
    st7789_wr_data(0x29);
    st7789_wr_data(0x33);
    st7789_wr_data(0x3E);
    st7789_wr_data(0x38);
    st7789_wr_data(0x12);
    st7789_wr_data(0x12);
    st7789_wr_data(0x28);
    st7789_wr_data(0x30);

    //Negative Voltage Gamma Control
    st7789_wr_cmd(0xE1);
    st7789_wr_data(0xF0);
    st7789_wr_data(0x07);
    st7789_wr_data(0x0A);
    st7789_wr_data(0x0D);
    st7789_wr_data(0x0B);
    st7789_wr_data(0x07);
    st7789_wr_data(0x28);
    st7789_wr_data(0x33);
    st7789_wr_data(0x3E);
    st7789_wr_data(0x36);
    st7789_wr_data(0x14);
    st7789_wr_data(0x14);
    st7789_wr_data(0x29);
    st7789_wr_data(0x32);

    //Display Inversion On
    st7789_wr_cmd(0x21);

    //sleep out
    st7789_wr_cmd(0x11);
    nrf_delay_ms(120);//TODO

    //Display On
    st7789_wr_cmd(0x29);

    nrf_delay_ms(20);
}

//SPI DMA完成中断
static void st7789_send_finish_callback(void)
{
    nrf_gpio_pin_write(GPIO_LCD_CS, 1);

    //log("st7789_send_finish_callback");

    //通知LVGL传输完成
    extern void call_lv_disp_flush_ready(void);
    call_lv_disp_flush_ready();
}

//写指令
static void st7789_wr_cmd(uint8_t byte)
{
    nrf_gpio_pin_write(GPIO_LCD_DC, 0);
    nrf_gpio_pin_write(GPIO_LCD_CS, 0);
    spi_st7789_send(&byte, 1, NULL);
    nrf_gpio_pin_write(GPIO_LCD_CS, 1);
}

//写数据
static void st7789_wr_data(uint8_t byte)
{
    nrf_gpio_pin_write(GPIO_LCD_DC, 1);
    nrf_gpio_pin_write(GPIO_LCD_CS, 0);
    spi_st7789_send(&byte, 1, NULL);
    nrf_gpio_pin_write(GPIO_LCD_CS, 1);
}

//设置RAM区域
static void st7789_set_addr_range(int16_t x1, int16_t y1, int16_t x2, int16_t y2)
{
    //LCM实际连线映射
    if(LCD_DIRECTION == 0) {
        x1 += 34;
        x2 += 34;
    } else if (LCD_DIRECTION == 1) {
        x1 += 34;
        x2 += 34;
    } else if (LCD_DIRECTION == 2) {
        y1 += 34;
        y2 += 34;
    } else {
        y1 += 34;
        y2 += 34;
    }

    //Column Address Set
    st7789_wr_cmd(0x2A);
    st7789_wr_data(x1 >> 8);
    st7789_wr_data(x1 & 0xFF);
    st7789_wr_data(x2 >> 8);
    st7789_wr_data(x2 & 0xFF);

    //Row Address Set
    st7789_wr_cmd(0x2B);
    st7789_wr_data(y1 >> 8);
    st7789_wr_data(y1 & 0xFF);
    st7789_wr_data(y2 >> 8);
    st7789_wr_data(y2 & 0xFF);
}

//填充区域，(x2, y2)点也会画出来
void st7789_wr_aera(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t* color_p)
{
    //检查坐标是否合理
    if (x1 < 0 || x1 >= LCD_HOR_RES || x2 < 0 || x2 >= LCD_HOR_RES ||
        y1 < 0 || y1 >= LCD_VER_RES || y2 < 0 || y2 >= LCD_VER_RES ||
        x1 > x2 || y1 > y2) {
        return;
    }

    //log("st7789_wr_aera: (%3d, %3d), (%3d, %3d)", x1, y1, x2, y2);

    //设置RAM区域
    st7789_set_addr_range(x1, y1, x2, y2);

    //开始写RAM
    st7789_wr_cmd(0x2C);

    //写数据
    nrf_gpio_pin_write(GPIO_LCD_DC, 1);
    nrf_gpio_pin_write(GPIO_LCD_CS, 0);

    //需要写入的数据字节数
    uint32_t data_len = (x2 - x1 + 1) * (y2 - y1 + 1) * 2;

    //DMA开始发送并回调
    spi_st7789_send((uint8_t*)color_p, data_len, st7789_send_finish_callback);
}

//整屏刷同一个颜色
void st7789_fill_full(uint16_t color)
{
    //设置RAM区域
    st7789_set_addr_range(0, 0, LCD_HOR_RES - 1, LCD_VER_RES - 1);

    //开始写RAM
    st7789_wr_cmd(0x2C);

    //写颜色
    nrf_gpio_pin_write(GPIO_LCD_DC, 1);
    nrf_gpio_pin_write(GPIO_LCD_CS, 0);
    for (uint32_t i = 0; i < LCD_HOR_RES * LCD_VER_RES; i++)
        spi_st7789_send((uint8_t*)&color, 2, NULL);
    nrf_gpio_pin_write(GPIO_LCD_CS, 1);
}

//画点
void st7789_wr_point(int16_t x, int16_t y, uint16_t color)
{
    //检查坐标是否合理
    if (x < 0 || x >= LCD_HOR_RES || y < 0 || y >= LCD_VER_RES) {
        return;
    }

    //log("st7789_wr_point: (%3d, %3d)\n", x, y);

    //设置RAM区域
    st7789_set_addr_range(x, y, x, y);

    //开始写RAM
    st7789_wr_cmd(0x2C);

    //写颜色
    st7789_wr_data(color & 0xFF);
    st7789_wr_data(color >> 8);
}

void close_LCD(void)
{
    st7789_wr_cmd(0x28);
}