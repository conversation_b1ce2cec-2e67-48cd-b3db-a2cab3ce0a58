T0B94 68871:783.549   SEGGER J-Link V7.94i Log File
T0B94 68871:784.114   DLL Compiled: Feb  7 2024 17:04:01
T0B94 68871:784.127   Logging started @ 2025-07-23 04:08
T0B94 68871:784.139   Process: D:\Keil_mdk\UV4\UV4.exe
T0B94 68871:784.271 - 68871784.149ms
T0B94 68871:784.432 JLINK_SetWarnOutHandler(...)
T0B94 68871:784.446 - 0.146ms
T0B94 68871:784.573 JLINK_OpenEx(...)
T0B94 68871:789.878   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0B94 68871:791.097   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0B94 68871:791.605   Decompressing FW timestamp took 396 us
T0B94 68871:801.674   Hardware: V9.40
T0B94 68871:801.733   S/N: 69409381
T0B94 68871:801.768   OEM: SEGGER
T0B94 68871:801.801   Feature(s): RDI, GDB, FlashDL, FlashBP, JFlash
T0B94 68871:803.587   Bootloader: (Could not read)
T0B94 68871:805.551   TELNET listener socket opened on port 19021
T0B94 68871:805.824   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T0B94 68871:806.161   WEBSRV Webserver running on local port 19080
T0B94 68871:806.405   Looking for J-Link GUI Server exe at: D:\Keil_mdk\ARM\Segger\JLinkGUIServer.exe
T0B94 68871:806.600   Looking for J-Link GUI Server exe at: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T0B94 68871:806.647   Forking J-Link GUI Server: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T0B94 68872:046.399   J-Link GUI Server info: "J-Link GUI server V7.94i "
T0B94 68872:048.974 - 264.247ms returns "O.K."
T0B94 68872:049.020 JLINK_GetEmuCaps()
T0B94 68872:049.035 - 0.013ms returns 0xB9FF7BBF
T0B94 68872:049.056 JLINK_TIF_GetAvailable(...)
T0B94 68872:049.454 - 0.398ms
T0B94 68872:049.489 JLINK_SetErrorOutHandler(...)
T0B94 68872:049.502 - 0.012ms
T0B94 68872:049.929 JLINK_ExecCommand("ProjectFile = "D:\Desktop\project\nRF5_SDK_17.1.0_ddde560_10_new\nRF5_SDK_17.1.0_ddde560\examples\ble_peripheral\ble_app_template\pca10056\s140\arm5_no_packs\JLinkSettings.ini"", ...). 
T0B94 68872:079.942   Ref file found at: D:\Keil_mdk\ARM\Segger\JLinkDevices.ref
T0B94 68872:080.103   REF file references invalid XML file: D:\jlink\JLink_V794i\JLinkDevices.xml
T0B94 68872:081.204 - 31.275ms returns 0x00
T0B94 68872:084.049 JLINK_ExecCommand("Device = nRF52840_xxAA", ...). 
T0B94 68872:087.667   Device "NRF52840_XXAA" selected.
T0B94 68872:088.054 - 3.971ms returns 0x00
T0B94 68872:088.076 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T0B94 68872:088.091 - 0.001ms returns 0x01
T0B94 68872:088.110 JLINK_GetHardwareVersion()
T0B94 68872:088.125 - 0.011ms returns 94000
T0B94 68872:088.138 JLINK_GetDLLVersion()
T0B94 68872:088.148 - 0.010ms returns 79409
T0B94 68872:088.160 JLINK_GetOEMString(...)
T0B94 68872:088.441 JLINK_GetFirmwareString(...)
T0B94 68872:088.471 - 0.029ms
T0B94 68872:093.633 JLINK_GetDLLVersion()
T0B94 68872:093.670 - 0.036ms returns 79409
T0B94 68872:093.690 JLINK_GetCompileDateTime()
T0B94 68872:093.702 - 0.012ms
T0B94 68872:095.177 JLINK_GetFirmwareString(...)
T0B94 68872:095.203 - 0.026ms
T0B94 68872:096.581 JLINK_GetHardwareVersion()
T0B94 68872:096.604 - 0.022ms returns 94000
T0B94 68872:098.081 JLINK_GetSN()
T0B94 68872:098.108 - 0.026ms returns 69409381
T0B94 68872:099.540 JLINK_GetOEMString(...)
T0B94 68872:102.702 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T0B94 68872:104.575 - 1.872ms returns 0x00
T0B94 68872:104.600 JLINK_HasError()
T0B94 68872:104.734 JLINK_SetSpeed(1000)
T0B94 68872:105.004 - 0.272ms
T0B94 68872:105.022 JLINK_GetId()
T0B94 68872:107.245   InitTarget() start
T0B94 68872:107.285    J-Link Script File: Executing InitTarget()
T0B94 68872:204.260   InitTarget() end - Took 92.9ms
T0B94 68872:380.924   InitTarget() start
T0B94 68872:381.020    J-Link Script File: Executing InitTarget()
T0B94 68872:482.155   InitTarget() end - Took 94.5ms
T0B94 68872:591.027 - 486.000ms returns 0x00000000
T0B94 68877:260.392 JLINK_Close()
T0B94 68877:269.991 - 9.598ms
T0B94 68877:270.020   
T0B94 68877:270.031   Closed
