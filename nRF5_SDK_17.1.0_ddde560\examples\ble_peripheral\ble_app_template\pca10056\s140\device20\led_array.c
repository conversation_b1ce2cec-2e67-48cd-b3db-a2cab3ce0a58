#include "led_array.h"
#include "nrf_drv_timer.h"
#include "pwm.h"



//IO速度优化代码
#define pin_write_high(pin_number)      {NRF_P0->OUTSET = (1UL << pin_number);}
#define pin_write_low(pin_number)       {NRF_P0->OUTCLR = (1UL << pin_number);}
#define pin_output(pin_number)          {NRF_P0->PIN_CNF[pin_number] |= 1UL;}
#define pin_input_no_pull(pin_number)   {NRF_P0->PIN_CNF[pin_number] &=~ 1UL;}



#define LED_ARRAY_COLS  (5)
#define LED_ARRAY_ROWS  (20)



APP_TIMER_DEF(m_page_timer_id);     //供显示页面调用的定时器，共用此一个定时器
app_timer_id_t m_page_timer;        //句柄，供调用
static page_timer_timeout_handler_t m_page_timer_timeout_handler = NULL;//页面定时器回调函数
const nrf_drv_timer_t TIMER_LED_ARRAY = NRF_DRV_TIMER_INSTANCE(1);//刷屏定时器
//static uint32_t m_dis_data[LED_ARRAY_COLS] = {0};       //显存，用于点阵刷新
static uint32_t m_render_data[LED_ARRAY_COLS] = {0};    //显存，用于渲染
//static bool m_led_array_refreshing = false;             //正在更新点阵的标志
//static const uint8_t m_bmp_digit[10][5] =
//{
//    {0x07, 0x05, 0x05, 0x05, 0x07},//0
//    {0x06, 0x02, 0x02, 0x02, 0x07},//1
//    {0x07, 0x01, 0x07, 0x04, 0x07},//2
//    {0x07, 0x01, 0x03, 0x01, 0x07},//3
//    {0x05, 0x05, 0x07, 0x01, 0x01},//4
//    {0x07, 0x04, 0x07, 0x01, 0x07},//5
//    {0x07, 0x04, 0x07, 0x05, 0x07},//6
//    {0x07, 0x01, 0x01, 0x01, 0x01},//7
//    {0x07, 0x05, 0x07, 0x05, 0x07},//8
//    {0x07, 0x05, 0x07, 0x01, 0x07},//9
//};






//串并转换推送点阵列数据
//TODO 优化速度
//static void write_row_data(uint32_t data)
//{
//
//    for (uint32_t i = 0; i < LED_ARRAY_ROWS; i++)
//    {
//        if (data & (1 << (LED_ARRAY_ROWS - 1 - i)))
//            {pin_write_low(SP_SER_PIN);}
//        else
//            {pin_write_high(SP_SER_PIN);}
//        pin_write_low(SP_SHCP_PIN);
//        pin_write_high(SP_SHCP_PIN);
//    }
//    pin_write_low(SP_SER_PIN);
//    pin_write_low(SP_STCP_PIN);
//    pin_write_high(SP_STCP_PIN);
//
//}


//刷屏定时器回调
//耗时6us
//static void timer_led_array_refreash_handler(nrf_timer_event_t event_type, void* p_context)
//{
//    (void)p_context;
//    static uint32_t cur_col = 0;
//
//    const static uint32_t col_pins[5] = {LED_COL1_PIN, LED_COL2_PIN, LED_COL3_PIN, LED_COL4_PIN, LED_COL5_PIN};
//
//    if (event_type != NRF_TIMER_EVENT_COMPARE0)
//    {
//        return;
//    }
//
//    m_led_array_refreshing = true;
//
//    pin_output(col_pins[cur_col]);
//    pin_write_low(col_pins[cur_col]);
//    if (++cur_col == 5) cur_col = 0;
//    write_row_data(m_dis_data[cur_col]);
//    pin_input_no_pull(col_pins[cur_col]);
//
//    m_led_array_refreshing = false;
//}


//页面定时器回调函数
static void page_animation_timeout_handler(void * p_context)
{
    (void)p_context;

    if (m_page_timer_timeout_handler != NULL) {
        m_page_timer_timeout_handler();
    }
}


//点阵初始化
void led_array_init(void)
{
    ret_code_t ret_code;

    //IO初始化
    /*
    nrf_gpio_cfg_output(SP_SHCP_PIN);//移位时钟，上升沿时作用
    nrf_gpio_pin_write(SP_SHCP_PIN, 0);
    nrf_gpio_cfg_output(SP_STCP_PIN);//映射时钟，上升沿时作用
    nrf_gpio_pin_write(SP_STCP_PIN, 0);
    nrf_gpio_cfg_output(SP_SER_PIN);//串行输入
    nrf_gpio_pin_write(SP_SER_PIN, 0);
    nrf_gpio_cfg_output(LED_COL1_PIN);
    nrf_gpio_pin_write(LED_COL1_PIN, 0);
    nrf_gpio_cfg_output(LED_COL2_PIN);
    nrf_gpio_pin_write(LED_COL2_PIN, 0);
    nrf_gpio_cfg_output(LED_COL3_PIN);
    nrf_gpio_pin_write(LED_COL3_PIN, 0);
    nrf_gpio_cfg_output(LED_COL4_PIN);
    nrf_gpio_pin_write(LED_COL4_PIN, 0);
    nrf_gpio_cfg_output(LED_COL5_PIN);
    nrf_gpio_pin_write(LED_COL5_PIN, 0);
    */
    //定时器
    ret_code = app_timer_create(&m_page_timer_id, APP_TIMER_MODE_REPEATED, page_animation_timeout_handler);
    APP_ERROR_CHECK(ret_code);
    m_page_timer_timeout_handler = NULL;
    /*
       //刷屏定时器
       nrf_drv_timer_config_t timer_cfg = NRF_DRV_TIMER_DEFAULT_CONFIG;
       timer_cfg.interrupt_priority = APP_IRQ_PRIORITY_HIGHEST;//有协议栈时最高优先级为2
       timer_cfg.frequency = NRF_TIMER_FREQ_16MHz;//定时器时钟频率
       ret_code = nrf_drv_timer_init(&TIMER_LED_ARRAY, &timer_cfg, timer_led_array_refreash_handler);
       APP_ERROR_CHECK(ret_code);
       uint32_t time_ticks = nrf_drv_timer_ms_to_ticks(&TIMER_LED_ARRAY, 1);
       nrf_drv_timer_extended_compare(&TIMER_LED_ARRAY, NRF_TIMER_CC_CHANNEL0, time_ticks, NRF_TIMER_SHORT_COMPARE0_CLEAR_MASK, true);
       nrf_drv_timer_enable(&TIMER_LED_ARRAY);

       //清屏
       led_array_clear();
       led_array_update();
    */
}


//点阵反初始化
void led_array_deinit(void)
{
    nrf_gpio_cfg_input(SP_SHCP_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(SP_STCP_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(SP_SER_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_COL1_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_COL2_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_COL3_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_COL4_PIN, NRF_GPIO_PIN_NOPULL);
    nrf_gpio_cfg_input(LED_COL5_PIN, NRF_GPIO_PIN_NOPULL);

    //刷屏定时器
    nrf_drv_timer_uninit(&TIMER_LED_ARRAY);
}


//开始页面定时器
void page_start_timer(uint32_t interval_ms, page_timer_timeout_handler_t handler)
{
    ret_code_t ret_code;

    m_page_timer_timeout_handler = handler;

    ret_code = app_timer_stop(m_page_timer_id);
    APP_ERROR_CHECK(ret_code);
    ret_code = app_timer_start(m_page_timer_id, APP_TIMER_TICKS(interval_ms), NULL);
    APP_ERROR_CHECK(ret_code);
}


//停止页面定时器
void page_stop_timer(void)
{
    ret_code_t ret_code;

    m_page_timer_timeout_handler = NULL;

    ret_code = app_timer_stop(m_page_timer_id);
    APP_ERROR_CHECK(ret_code);
}


//设置点阵亮度
void led_array_set_bright(uint32_t bright)
{
    APP_ERROR_CHECK_BOOL(bright <= 100);

    pwm_led_array_set_duty_percent(bright);
}


//将数据更新到显示驱动
void led_array_update(void)
{
//    while (m_led_array_refreshing == true) {}
//    memcpy(m_dis_data, m_render_data, sizeof(m_dis_data));
}


//拷贝
void led_array_copy(uint32_t* data)
{
//    memcpy(m_render_data, data, sizeof(m_render_data));
}


//点阵：清除全部
void led_array_clear(void)
{
//   memset(m_render_data, 0, sizeof(m_render_data));
}


//点阵：画点
void led_array_point(uint32_t x, uint32_t y, uint32_t color)
{
    APP_ERROR_CHECK_BOOL(x <= 19);
    APP_ERROR_CHECK_BOOL(y <= 4);

    if (color) {
        m_render_data[y] |= (1 << (LED_ARRAY_ROWS - 1 - x));
    } else {
        m_render_data[y] &=~ (1 << (LED_ARRAY_ROWS - 1 - x));
    }
}


//点阵：矩形
void led_array_rectangle(uint32_t x1, uint32_t y1, uint32_t x2, uint32_t y2, uint32_t color)
{
    for (uint32_t i = x1; i <= x2; i++) {
        for (uint32_t j = y1; j <= y2; j++) {
            led_array_point(i, j, color);
        }
    }
}


//点阵：横线
void led_array_hor_line(uint32_t x1, uint32_t x2, uint32_t y, uint32_t color)
{
    APP_ERROR_CHECK_BOOL(x1 <= 19);
    APP_ERROR_CHECK_BOOL(x2 <= 19);
    APP_ERROR_CHECK_BOOL(y <= 4);

    if (x1 > x2) {
        uint32_t temp = x1;
        x1 = x2;
        x2 = temp;
    }

    if (color) {
        for (uint32_t i = x1; i <= x2; i++) {
            m_render_data[y] |= (1 << (LED_ARRAY_ROWS - 1 - i));
        }
    } else {
        for (uint32_t i = x1; i <= x2; i++) {
            m_render_data[y] &=~ (1 << (LED_ARRAY_ROWS - 1 - i));
        }
    }
}


//点阵：竖线
void led_array_ver_line(uint32_t x, uint32_t y1, uint32_t y2, uint32_t color)
{
    APP_ERROR_CHECK_BOOL(x <= 19);
    APP_ERROR_CHECK_BOOL(y1 <= 4);
    APP_ERROR_CHECK_BOOL(y2 <= 4);

    if (y1 > y2) {
        uint32_t temp = y1;
        y1 = y2;
        y2 = temp;
    }

    if (color) {
        for (uint32_t i = y1; i <= y2; i++) {
            m_render_data[i] |= (1 << (LED_ARRAY_ROWS - 1 - x));
        }
    } else {
        for (uint32_t i = y1; i <= y2; i++) {
            m_render_data[i] &=~ (1 << (LED_ARRAY_ROWS - 1 - x));
        }
    }
}


//点阵：单个数字
void led_array_digit(uint32_t x, uint32_t digit, uint32_t color)
{
    APP_ERROR_CHECK_BOOL(x <= 17);
    APP_ERROR_CHECK_BOOL(digit <= 9);
    /*
     if (color)
     {
         for (uint32_t i = 0; i < 5; i++)
         {
             m_render_data[i] |= (m_bmp_digit[digit][i] << (17 - x));
         }
     }
     else
     {
         for (uint32_t i = 0; i < 5; i++)
         {
             m_render_data[i] &=~ (m_bmp_digit[digit][i] << (17 - x));
         }
     }
    */
}

