#ifndef __FRAME_H__
#define __FRAME_H__


#include "main.h"



typedef struct {
    uint32_t payload_len;	//payload结构体的长度
    struct {
        uint8_t cmd;        //指令号
        uint8_t para_len;   //表示para的长度
        uint8_t para[256];  //参数
    } payload;
} nus_data_t;


void frame_recv_ble_data(const uint8_t* p_data, uint16_t length);
void frame_send_frame(nus_data_t* p_nus_data);
void ble_low_layer_send_data(void);
void frame_send_string(char* str);
void frame_clear(void);
void frame_init(void);



#endif
