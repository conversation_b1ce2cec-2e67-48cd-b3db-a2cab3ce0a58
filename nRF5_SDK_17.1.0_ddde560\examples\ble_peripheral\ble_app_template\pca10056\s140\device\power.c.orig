#include "power.h"
#include "nrf_gpio.h"
#include "nrf_drv_gpiote.h"
#include "nrf_drv_power.h"
#include "app_timer.h"
#include "gui.h"
#include "sm.h"

#define STATE_STABLE_THRESHOLD  2  // 连续3次一样才切换状态
/**LGS4056H充电管理IC**/

APP_TIMER_DEF(m_lgs4056_timer);

static charge_state_t current_charge_state = CHARGE_STATE_IDLE;
static charge_state_t last_sampled_state = CHARGE_STATE_IDLE;
static uint8_t stable_count = 0;
static void lgs4056_timer_handler(void *p_context);
static void lgs4056_update_state(charge_state_t new_state);

void lgs4056_gpio_init(void)
{
    ret_code_t err_code;

    if (!nrf_drv_gpiote_is_init()) {
        err_code = nrf_drv_gpiote_init();
        APP_ERROR_CHECK(err_code);
    }

    // 配置引脚为上拉输入
    nrf_gpio_cfg_input(LGS4056_CHRG_PIN, NRF_GPIO_PIN_PULLUP);
    nrf_gpio_cfg_input(LGS4056_DONE_PIN, NRF_GPIO_PIN_PULLUP);

    // 创建并启动定时器
    err_code = app_timer_create(&m_lgs4056_timer, APP_TIMER_MODE_REPEATED, lgs4056_timer_handler);
    APP_ERROR_CHECK(err_code);

    err_code = app_timer_start(m_lgs4056_timer, APP_TIMER_TICKS(300), NULL);  // 300ms 定时器轮询
    APP_ERROR_CHECK(err_code);
}

static void lgs4056_timer_handler(void *p_context)
{
    bool chrg = nrf_gpio_pin_read(LGS4056_CHRG_PIN);   // 0 = 正在充电
    bool done = nrf_gpio_pin_read(LGS4056_DONE_PIN);   // 0 = 充满

    charge_state_t sampled;

    if (chrg == 0) {
        sampled = CHARGE_STATE_CHARGING;
    } else if (done == 0) {
        sampled = CHARGE_STATE_CHARGED;
    } else {
        sampled = CHARGE_STATE_IDLE;
    }

    // 状态一致性判断
    if (sampled == last_sampled_state) {
        stable_count++;
        if (stable_count >= STATE_STABLE_THRESHOLD && sampled != current_charge_state) {
            lgs4056_update_state(sampled);
        }
    } else {
        stable_count = 0;
        last_sampled_state = sampled;
    }
}

static void lgs4056_update_state(charge_state_t new_state)
{
    current_charge_state = new_state;

    switch (new_state) {
    case CHARGE_STATE_CHARGING:
        if (get_current_gui() != UI_SCREEN_CHARGED) {
            set_current_gui(UI_SCREEN_CHARGED);
            switch_to_next_screen(UI_SCREEN_CHARGED);
        }
        break;

    case CHARGE_STATE_IDLE:
        if (get_current_gui() != UI_SCREEN_RESULT1) {
            set_current_gui(UI_SCREEN_RESULT1);
            switch_to_next_screen(UI_SCREEN_RESULT1);
        }
        break;

    case CHARGE_STATE_CHARGED:
        // 你可以在这里处理充满电的提示界面或跳转
        break;
    }
}

//电量显示
void format_percentage(int value, char *output_buffer, size_t buffer_size)
{
    // 限制范围在 0~100
    if (value < 0) value = 0;
    if (value > 100) value = 100;

    snprintf(output_buffer, buffer_size, "%d%%", value);
}


////电源初始化
//void power_init(void)
//{
//    ret_code_t err_code;
//    err_code = nrf_pwr_mgmt_init();
//    APP_ERROR_CHECK(err_code);
//
//#if NRF_MODULE_ENABLED(POWER)
//	ret_code_t ret = nrf_drv_power_init(NULL);
//	APP_ERROR_CHECK(ret);
//#endif
//}

////睡眠
//void power_wait(void)
//{
//	//处理定时器的调度
//	app_sched_execute();//必须加这句话，不然会卡死
//
//	//睡眠
//	if (!NRF_LOG_PROCESS())
//	{
//		nrf_pwr_mgmt_run();
//	}
//}

////休眠
//void power_sleep(void)
//{
//    ret_code_t err_code;

//	//先准备好唤醒条件
//
//	NRF_LOG_INFO("power_sleep\n");
//
//    // Go to system-off mode (this function will not return; wakeup will cause a reset).
//	//如果系统在Debug（RTT打开）模式下，调用sd_power_system_off()将会返回NRF_ERROR_SOC_POWER_OFF_SHOULD_NOT_RETURN
//    err_code = sd_power_system_off();
//	if (err_code == NRF_ERROR_SOC_POWER_OFF_SHOULD_NOT_RETURN)
//		APP_ERROR_CHECK(err_code);
//}

////复位
//void power_reset(void)
//{
//	if (!NRF_LOG_PROCESS())
//	{
//		NVIC_SystemReset();
//	}
//}
