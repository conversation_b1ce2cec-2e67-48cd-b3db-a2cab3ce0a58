#ifndef __BM8563_H__
#define __BM8563_H__


#include "main.h"

#define BM8563ADDR 0xA2
typedef struct times {
    int Year;
    int Mon;
    int Day;
    int Hour;
    int Min;
    int Second;
} Times;

typedef struct tm {
    int tm_sec;         /* ?,??? 0 ? 59        */
    int tm_min;         /* ?,??? 0 ? 59        */
    int tm_hour;        /* ??,??? 0 ? 23        */
    int tm_mday;        /* ???????,??? 1 ? 31    */
    int tm_wday;        /* ???????,??? 0 ? 6    */
    int tm_mon;         /* ?,??? 0 ? 11        */
    int tm_year;        /* ? 1900 ?????        */
    int tm_yday;        /* ???????,??? 0 ? 365    */
    int tm_isdst;       /* ???                */
} Tm;
uint8_t  IICInint(void);
uint8_t BM8563Init(void);
uint8_t GetBM8563(uint8_t suba,uint8_t *s,uint8_t no);
uint8_t SetBM8563(uint8_t suba,uint8_t *s,uint8_t no);
uint32_t  GetTimeStamp(void);
bool SetTimeFromStamp(uint32_t mstamp,uint32_t mzone,uint32_t wday);
void LocalTime(uint32_t time,Tm *tm);
unsigned long mktime(const unsigned int year0, const unsigned int mon0,
                     const unsigned int day, const unsigned int hour,
                     const unsigned int min, const unsigned int sec);
#endif
