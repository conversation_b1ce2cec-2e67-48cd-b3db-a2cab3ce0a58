.\_build\frame.o: ..\device20\frame.c
.\_build\frame.o: ..\device20\frame.h
.\_build\frame.o: ..\config\main.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdint.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\string.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdio.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdbool.h
.\_build\frame.o: ..\config\sdk_config.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stddef.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_ctrl.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_ctrl_internal.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_backend_interface.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\memobj\nrf_memobj.h
.\_build\frame.o: D:\Keil_mdk\ARM\ARM960\Bin\..\include\stdlib.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\balloc\nrf_balloc.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_default_backends.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_err.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gap.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_hci.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_ranges.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_types.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_l2cap.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatt.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gattc.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\ble_gatts.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_gpio.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\frame.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf_peripherals.h
.\_build\frame.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_peripherals.h
.\_build\frame.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\frame.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\frame.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\delay\nrf_delay.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\timer\app_timer.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\sortlist\nrf_sortlist.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\fifo\app_fifo.h
.\_build\frame.o: ..\..\..\..\ble_app_template\protocol.h
.\_build\frame.o: ..\device20\storage.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\fds\fds.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\queue\nrf_queue.h
.\_build\frame.o: ..\..\..\..\..\..\components\ble\ble_services\ble_nus\ble_nus.h
.\_build\frame.o: ..\..\..\..\..\..\components\ble\common\ble_srv_common.h
.\_build\frame.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_ble.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.h
.\_build\frame.o: ..\..\..\..\..\..\components\ble\ble_link_ctx_manager\ble_link_ctx_manager.h
.\_build\frame.o: ..\..\..\..\..\..\components\ble\common\ble_conn_state.h
.\_build\frame.o: ..\..\..\..\..\..\components\libraries\atomic\nrf_atomic.h
