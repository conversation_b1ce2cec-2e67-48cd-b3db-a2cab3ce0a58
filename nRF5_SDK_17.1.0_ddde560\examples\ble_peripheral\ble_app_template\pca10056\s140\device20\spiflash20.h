#ifndef __SPIFLASH20_H__
#define __SPIFLASH20_H__

//ZD25WD20B  2M-bit

//Flexible Architecture for Code and Data Storage
//- Uniform 256-byte PageProgram
//- Uniform 256-byte Page Erase
//- Uniform 4K-byte Sector Erase
//- Uniform 32K/64K-byte Block Erase

#include "main.h"
#include "nrf_drv_spi.h"

void spi_event_handler(nrf_drv_spi_evt_t const * p_event, void *p_context);

void spi_master_init(void);

/*
向flash中写入数据
参数  data 要写入的数据
*/
void spi_flash_write_reg(uint8_t data);

/*
从flash中读取数据
参数： reg 寄存器地址
*/
uint8_t spi_flash_read_reg(uint8_t reg);


/*
读取flash的器件ID
*/
uint32_t spi_flash_ReadID(void);


/*
写使能命令
*/
void spi_flash_WriteEnable(void);


/*
通过读状态寄存器等待FLASH芯片空闲
*/
void spi_flash_WaitForWriteEnd(void);

/*
擦除FLASH的扇区
参数 SectorAddr 要擦除的扇区地址
*/
void spi_flash_FLASH_SectorErase(uint32_t SectorAddr);


/*
FLASH页写入指令
参数：
备注：使用页写入指令最多可以一次向FLASH传输256个字节的数据
*/
void spi_flash_FLASH_PageWrite(unsigned char* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);

/*
从FLASH中读取数据
*/
void spi_flash_Flash_BufferRead(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead);

/*
全片擦除
*/
void spi_flash_Chip_Erase(void);



void spi_flash_init(void);



#endif
